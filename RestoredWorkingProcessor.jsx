#target photoshop
//
// RestoredWorkingProcessor.jsx
// Your working import interface + the proven 11-step processing logic
//

cTID = function(s) { return app.charIDToTypeID(s); };
sTID = function(s) { return app.stringIDToTypeID(s); };

//==================== Restored Working Processor ==============
function RestoredWorkingProcessor() {

  // AUTOMATED FILE IMPORT WITH SUBSTRING MATCHING (your proven working version)
  function importFilesInOrder() {
    var shouldImport = confirm("Do you want to import image files?\n\nYES = Auto-import from folder\nNO = Use existing layers in current document");

    if (!shouldImport) {
      if (!app.documents.length) {
        alert("No document is open and no files will be imported. Please open a document first.");
        return false;
      }
      return true;
    }

    // Select folder containing images
    var sourceFolder = Folder.selectDialog("Select folder containing narrowband images:");
    if (!sourceFolder) {
      alert("No folder selected. <PERSON>ript cancelled.");
      return false;
    }

    // Get all image files from folder
    var imageFiles = sourceFolder.getFiles(/\.(tif|tiff|jpg|jpeg|png|psd|fits|fit)$/i);
    if (imageFiles.length === 0) {
      alert("No image files found in selected folder.");
      return false;
    }

    // Define layer matching patterns (case-insensitive)
    var layerPatterns = [
      {
        name: "Starless_RGB_Combined",
        description: "11. Starless RGB Combined",
        patterns: ["starless_rgb_combined", "starless rgb combined", "starless_rgb", "starless rgb", "starless color", "starless colour"]
      },
      {
        name: "Starless_L_RGB",
        description: "10. Starless L RGB", 
        patterns: ["starless_l_rgb", "starless lrgb", "starless l-rgb", "starless l rgb", "lrgb starless"]
      },
      {
        name: "Starless_L",
        description: "9. Starless L",
        patterns: ["starless_l", "starless l ", "starless lum", "starless luminance"]
      },
      {
        name: "Starless_NB_RGB",
        description: "8. Starless NB RGB",
        patterns: ["starless_nb_rgb", "starless nb rgb", "starless nbrgb", "starless nb-rgb", "starless narrowband rgb", "starless duo", "starless tri-band", "starless triband"]
      },
      {
        name: "Starless_HOO_Combined",
        description: "7. Starless HOO Combined",
        patterns: ["starless_hoo_combined", "starless hoo", "hoo starless", "starless-hoo", "starless_nb_hoo"]
      },
      {
        name: "Starless_SII",
        description: "6. Starless SII",
        patterns: ["SII", "sulfur", "sulphur", "S"]
      },
      {
        name: "Starless_OIII",
        description: "5. Starless OIII",
        patterns: ["OIII", "oxygen", "O"]
      },
      {
        name: "Starless_Ha",
        description: "4. Starless Ha",
        patterns: ["Ha", "hydrogen", "H"]
      },
      {
        name: "Stars_HOO_Combined",
        description: "3. Stars HOO Combined",
        patterns: ["stars_hoo_combined", "stars hoo", "hoo stars", "stars-hoo", "stars_hoo", "stars_nb_hoo"]
      },
      {
        name: "Stars_RGB_Combined",
        description: "2. Stars RGB Combined",
        patterns: ["stars_rgb_combined", "stars rgb combined", "stars_rgb", "starsrgb", "stars-rgb", "stars color", "stars colour"]
      },
      {
        name: "Annotation",
        description: "1. Annotation (TOP)",
        patterns: ["annot", "annotation", "note", "label"]
      }
    ];

    // Function to check if a keyword exists as a separate word or bounded by non-alphabetic chars
    function containsKeyword(fileName, keyword) {
      var lowerFileName = fileName.toLowerCase();
      var lowerKeyword = keyword.toLowerCase();

      // Find all occurrences of the keyword
      var index = 0;
      while ((index = lowerFileName.indexOf(lowerKeyword, index)) !== -1) {
        var beforeChar = index === 0 ? '' : lowerFileName.charAt(index - 1);
        var afterChar = index + lowerKeyword.length >= lowerFileName.length ? '' : lowerFileName.charAt(index + lowerKeyword.length);

        // Check if keyword is bounded by non-alphabetic characters
        var beforeOk = beforeChar === '' || !/[a-z]/.test(beforeChar);
        var afterOk = afterChar === '' || !/[a-z]/.test(afterChar);

        if (beforeOk && afterOk) {
          return true;
        }
        index++;
      }
      return false;
    }

    // Function to find matching file for a layer pattern
    function findMatchingFile(layerPattern) {
      for (var i = 0; i < imageFiles.length; i++) {
        var fileName = imageFiles[i].name;

        for (var j = 0; j < layerPattern.patterns.length; j++) {
          var pattern = layerPattern.patterns[j];
          if (containsKeyword(fileName, pattern)) {
            return imageFiles[i];
          }
        }
      }
      return null;
    }

    var doc = null;
    var importedLayers = [];
    var skippedLayers = [];

    // Process each layer pattern in reverse order (bottom to top stacking)
    for (var i = 0; i < layerPatterns.length; i++) {
      var layerPattern = layerPatterns[i];
      var matchingFile = findMatchingFile(layerPattern);

      if (matchingFile) {
        try {
          var sourceDoc = app.open(matchingFile);
          if (sourceDoc.layers.length > 1) {
            sourceDoc.flatten();
          }

          // For first image, create document by duplicating from source (no background layer approach)
          if (!doc) {
            // Create new document by duplicating the source document (avoids Layer 0 entirely)
            doc = sourceDoc.duplicate("Narrowband Processing");

            // Close the source document
            sourceDoc.close(SaveOptions.DONOTSAVECHANGES);

            // Make the new document active
            app.activeDocument = doc;

            // Rename the layer to the correct name
            doc.activeLayer.name = layerPattern.name;

          } else {
            // For subsequent images, duplicate normally
            // Make source document active for duplication
            app.activeDocument = sourceDoc;
            sourceDoc.activeLayer.duplicate(doc);
            sourceDoc.close(SaveOptions.DONOTSAVECHANGES);

            // Switch back to target document and rename the layer
            app.activeDocument = doc;
            doc.activeLayer.name = layerPattern.name;
          }

          // Make Annotation layer truly transparent with alpha channel
          if (layerPattern.name === "Annotation") {
            // Ensure we have alpha channel for transparency
            try {
              // Add alpha channel if it doesn't exist
              if (doc.channels.length < 4) {
                doc.channels.add();
              }

              // Set layer to use transparency (blend mode and opacity)
              doc.activeLayer.opacity = 75; // 75% opacity for visibility
              doc.activeLayer.blendMode = BlendMode.NORMAL; // Normal blend for proper transparency

              // Enable transparency for the layer
              doc.activeLayer.fillOpacity = 75;
            } catch (e) {
              // Fallback to just opacity if alpha channel operations fail
              doc.activeLayer.opacity = 75;
            }
          }

          importedLayers.push(layerPattern.description + " <- " + matchingFile.name);

        } catch (e) {
          skippedLayers.push(layerPattern.description + " (Error: " + e.message + ")");
        }
      } else {
        skippedLayers.push(layerPattern.description + " (No matching file found)");
      }
    }

    // Show import summary
    var summary = "IMPORT SUMMARY:\n\n";
    summary += "IMPORTED (" + importedLayers.length + "):\n";
    for (var i = 0; i < importedLayers.length; i++) {
      summary += "* " + importedLayers[i] + "\n";
    }

    if (skippedLayers.length > 0) {
      summary += "\nSKIPPED (" + skippedLayers.length + "):\n";
      for (var i = 0; i < skippedLayers.length; i++) {
        summary += "* " + skippedLayers[i] + "\n";
      }
      summary += "\nNote: Groups for skipped layers will be automatically excluded from processing.";
    }

    alert(summary);

    return doc !== null; // Return true if at least one file was imported
  }

  // Import files first
  if (!importFilesInOrder()) {
    return;
  }

  // Now execute the proven 11-step logic that actually works
  executeProven11StepLogic();
}

// Execute the first 11 steps from the working AllNarrowbandLayers.jsx
function executeProven11StepLogic() {
  alert("Executing proven 11-step processing logic...");

  // Execute the first 11 steps that create the basic structure
  step1();      // Make group
  step2();      // Set name to "Annotation"
  step3();      // Set blend mode to Screen
  step4();      // Hide
  step5();      // Select Stars_RGB_Combined
  step6();      // Select Stars_HOO_Combined (add to selection)
  step7();      // Make group
  step8();      // Set name to "Stars"
  step9();      // Select Stars_RGB_Combined
  step10();     // Set blend mode to Color
  step11();     // Make adjustment layer (Levels)

  alert("SUCCESS! Basic 11-step structure created with groups and adjustment layers!");
}

// THE PROVEN STEP FUNCTIONS FROM AllNarrowbandLayers.jsx

// Make group
function step1(enabled, withDialog) {
  if (enabled != undefined && !enabled)
    return;
  var dialogMode = (withDialog ? DialogModes.ALL : DialogModes.NO);
  var desc1 = new ActionDescriptor();
  var ref1 = new ActionReference();
  ref1.putClass(sTID("layerSection"));
  desc1.putReference(cTID('null'), ref1);
  var ref2 = new ActionReference();
  ref2.putEnumerated(cTID('Lyr '), cTID('Ordn'), cTID('Trgt'));
  desc1.putReference(cTID('From'), ref2);
  desc1.putInteger(sTID("layerSectionStart"), 26);
  desc1.putInteger(sTID("layerSectionEnd"), 27);
  desc1.putString(cTID('Nm  '), "Group 1");
  executeAction(cTID('Mk  '), desc1, dialogMode);
}

// Set name to "Annotation"
function step2(enabled, withDialog) {
  if (enabled != undefined && !enabled)
    return;
  var dialogMode = (withDialog ? DialogModes.ALL : DialogModes.NO);
  var desc1 = new ActionDescriptor();
  var ref1 = new ActionReference();
  ref1.putEnumerated(cTID('Lyr '), cTID('Ordn'), cTID('Trgt'));
  desc1.putReference(cTID('null'), ref1);
  var desc2 = new ActionDescriptor();
  desc2.putString(cTID('Nm  '), "Annotation");
  desc1.putObject(cTID('T   '), cTID('Lyr '), desc2);
  executeAction(cTID('setd'), desc1, dialogMode);
}

// Set blend mode to Screen
function step3(enabled, withDialog) {
  if (enabled != undefined && !enabled)
    return;
  var dialogMode = (withDialog ? DialogModes.ALL : DialogModes.NO);
  var desc1 = new ActionDescriptor();
  var ref1 = new ActionReference();
  ref1.putEnumerated(cTID('Lyr '), cTID('Ordn'), cTID('Trgt'));
  desc1.putReference(cTID('null'), ref1);
  var desc2 = new ActionDescriptor();
  desc2.putEnumerated(cTID('Md  '), cTID('BlnM'), cTID('Scrn'));
  desc1.putObject(cTID('T   '), cTID('Lyr '), desc2);
  executeAction(cTID('setd'), desc1, dialogMode);
}

// Hide layer
function step4(enabled, withDialog) {
  if (enabled != undefined && !enabled)
    return;
  var dialogMode = (withDialog ? DialogModes.ALL : DialogModes.NO);
  var desc1 = new ActionDescriptor();
  var list1 = new ActionList();
  var ref1 = new ActionReference();
  ref1.putEnumerated(cTID('Lyr '), cTID('Ordn'), cTID('Trgt'));
  list1.putReference(ref1);
  desc1.putList(cTID('null'), list1);
  executeAction(cTID('Hd  '), desc1, dialogMode);
}

// Select Stars_RGB_Combined
function step5(enabled, withDialog) {
  if (enabled != undefined && !enabled)
    return;
  var dialogMode = (withDialog ? DialogModes.ALL : DialogModes.NO);
  var desc1 = new ActionDescriptor();
  var ref1 = new ActionReference();
  ref1.putName(cTID('Lyr '), "Stars_RGB_Combined");
  desc1.putReference(cTID('null'), ref1);
  desc1.putBoolean(cTID('MkVs'), false);
  var list1 = new ActionList();
  list1.putInteger(3);
  desc1.putList(cTID('LyrI'), list1);
  executeAction(cTID('slct'), desc1, dialogMode);
}

// Select Stars_HOO_Combined (add to selection)
function step6(enabled, withDialog) {
  if (enabled != undefined && !enabled)
    return;
  var dialogMode = (withDialog ? DialogModes.ALL : DialogModes.NO);
  var desc1 = new ActionDescriptor();
  var ref1 = new ActionReference();
  ref1.putName(cTID('Lyr '), "Stars_HOO_Combined");
  desc1.putReference(cTID('null'), ref1);
  desc1.putEnumerated(sTID("selectionModifier"), sTID("selectionModifierType"), sTID("addToSelectionContinuous"));
  desc1.putBoolean(cTID('MkVs'), false);
  var list1 = new ActionList();
  list1.putInteger(5);
  list1.putInteger(3);
  desc1.putList(cTID('LyrI'), list1);
  executeAction(cTID('slct'), desc1, dialogMode);
}

// Make Stars group
function step7(enabled, withDialog) {
  if (enabled != undefined && !enabled)
    return;
  var dialogMode = (withDialog ? DialogModes.ALL : DialogModes.NO);
  var desc1 = new ActionDescriptor();
  var ref1 = new ActionReference();
  ref1.putClass(sTID("layerSection"));
  desc1.putReference(cTID('null'), ref1);
  var ref2 = new ActionReference();
  ref2.putEnumerated(cTID('Lyr '), cTID('Ordn'), cTID('Trgt'));
  desc1.putReference(cTID('From'), ref2);
  desc1.putInteger(sTID("layerSectionStart"), 28);
  desc1.putInteger(sTID("layerSectionEnd"), 29);
  desc1.putString(cTID('Nm  '), "Group 1");
  executeAction(cTID('Mk  '), desc1, dialogMode);
}

// Set name to "Stars"
function step8(enabled, withDialog) {
  if (enabled != undefined && !enabled)
    return;
  var dialogMode = (withDialog ? DialogModes.ALL : DialogModes.NO);
  var desc1 = new ActionDescriptor();
  var ref1 = new ActionReference();
  ref1.putEnumerated(cTID('Lyr '), cTID('Ordn'), cTID('Trgt'));
  desc1.putReference(cTID('null'), ref1);
  var desc2 = new ActionDescriptor();
  desc2.putString(cTID('Nm  '), "Stars");
  desc1.putObject(cTID('T   '), cTID('Lyr '), desc2);
  executeAction(cTID('setd'), desc1, dialogMode);
}

// Select Stars_RGB_Combined again
function step9(enabled, withDialog) {
  if (enabled != undefined && !enabled)
    return;
  var dialogMode = (withDialog ? DialogModes.ALL : DialogModes.NO);
  var desc1 = new ActionDescriptor();
  var ref1 = new ActionReference();
  ref1.putName(cTID('Lyr '), "Stars_RGB_Combined");
  desc1.putReference(cTID('null'), ref1);
  desc1.putBoolean(cTID('MkVs'), false);
  var list1 = new ActionList();
  list1.putInteger(3);
  desc1.putList(cTID('LyrI'), list1);
  executeAction(cTID('slct'), desc1, dialogMode);
}

// Set blend mode to Color
function step10(enabled, withDialog) {
  if (enabled != undefined && !enabled)
    return;
  var dialogMode = (withDialog ? DialogModes.ALL : DialogModes.NO);
  var desc1 = new ActionDescriptor();
  var ref1 = new ActionReference();
  ref1.putEnumerated(cTID('Lyr '), cTID('Ordn'), cTID('Trgt'));
  desc1.putReference(cTID('null'), ref1);
  var desc2 = new ActionDescriptor();
  desc2.putEnumerated(cTID('Md  '), cTID('BlnM'), cTID('Clr '));
  desc1.putObject(cTID('T   '), cTID('Lyr '), desc2);
  executeAction(cTID('setd'), desc1, dialogMode);
}

// Make Levels adjustment layer
function step11(enabled, withDialog) {
  if (enabled != undefined && !enabled)
    return;
  var dialogMode = (withDialog ? DialogModes.ALL : DialogModes.NO);
  var desc1 = new ActionDescriptor();
  var desc2 = new ActionDescriptor();
  desc2.putString(sTID("failureLabel"), "make adjustment layer");
  desc1.putObject(sTID("_options"), cTID('null'), desc2);
  var ref1 = new ActionReference();
  ref1.putClass(cTID('AdjL'));
  desc1.putReference(cTID('null'), ref1);
  var desc3 = new ActionDescriptor();
  var desc4 = new ActionDescriptor();
  desc3.putObject(cTID('Type'), cTID('Lvls'), desc4);
  desc1.putObject(cTID('Usng'), cTID('AdjL'), desc3);
  executeAction(cTID('Mk  '), desc1, dialogMode);
}

// Main execution
RestoredWorkingProcessor();
